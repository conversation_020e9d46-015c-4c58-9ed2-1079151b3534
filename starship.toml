# 🚀 超级创意 Starship 配置
# 赛博朋克风格 + 动态元素 + 极致美化

# 全局格式设置 - 赛博朋克风格
format = """
[](fg:#a3aed2 bg:#394260)\
[](fg:#394260 bg:#232738)\
$os\
$username\
[](fg:#232738 bg:#394260)\
$directory\
[](fg:#394260 bg:#212736)\
$git_branch\
$git_status\
[](fg:#212736 bg:#1d2230)\
$nodejs\
$python\
$rust\
$golang\
$java\
$c\
$elixir\
$elm\
$gradle\
$haskell\
$julia\
$kotlin\
$lua\
$nim\
$ruby\
$scala\
$swift\
[](fg:#1d2230 bg:#16181d)\
$docker_context\
$conda\
[](fg:#16181d bg:#0d1117)\
$time\
[ ](fg:#0d1117)\
$line_break\
$character"""

# 右侧提示符 - 动态状态栏
right_format = """$cmd_duration$jobs$battery$memory_usage"""

# 字符设置 - 赛博朋克风格
[character]
success_symbol = "[](bold green)"
error_symbol = "[](bold red)"
vimcmd_symbol = "[](bold yellow)"

# 换行设置
[line_break]
disabled = false

# 操作系统 - 赛博朋克风格
[os]
disabled = false
style = "bg:#394260 fg:#ffffff"
format = '[ $symbol ]($style)'

[os.symbols]
Windows = "󰍲"
Ubuntu = "󰕈"
SUSE = ""
Raspbian = "󰐿"
Mint = "󰣭"
Macos = "󰀵"
Manjaro = ""
Linux = "󰌽"
Gentoo = "󰣨"
Fedora = "󰣛"
Alpine = ""
Amazon = ""
Android = ""
Arch = "󰣇"
Artix = "󰣇"
CentOS = ""
Debian = "󰣚"
Redhat = "󱄛"
RedHatEnterprise = "󱄛"

# 用户名 - 霓虹灯效果
[username]
show_always = true
style_user = "bg:#232738 fg:#ffffff"
style_root = "bg:#232738 fg:#ff0000"
format = '[ 󰀄 $user ]($style)'

# 主机名
[hostname]
ssh_only = false
style = "bg:#232738 fg:#ffffff"
format = '[@$hostname ]($style)'

# 目录 - 发光路径
[directory]
style = "fg:#e3e5e5 bg:#394260"
format = "[ $path ]($style)"
truncation_length = 3
truncation_symbol = "…/"
read_only = "󰌾"

[directory.substitutions]
"Documents" = "󰈙"
"Downloads" = ""
"Music" = "󰝚"
"Pictures" = ""
"Videos" = ""
"Desktop" = "󰧨"
"Projects" = "󰲋"
"Code" = ""
".config" = ""
"src" = ""
"node_modules" = ""
".git" = ""

# Git 分支 - 量子风格
[git_branch]
symbol = ""
style = "bg:#212736 fg:#1C3A5E"
format = '[ $symbol $branch ]($style)'

# Git 状态 - 矩阵风格
[git_status]
style = "bg:#212736 fg:#1C3A5E"
format = '[$all_status$ahead_behind ]($style)'
conflicted = "🏳"
ahead = "⇡${count}"
behind = "⇣${count}"
diverged = "⇕⇡${ahead_count}⇣${behind_count}"
up_to_date = "✓"
untracked = "?${count}"
stashed = "📦${count}"
modified = "!${count}"
staged = "+${count}"
renamed = "»${count}"
deleted = "✘${count}"

# Git 指标 - 数据流风格
[git_metrics]
disabled = false
added_style = "bg:#212736 fg:#1C3A5E"
deleted_style = "bg:#212736 fg:#1C3A5E"
format = '[+$added]($added_style)[  ]()[−$deleted]($deleted_style)'

# 包管理器 - 全息投影风格
[package]
symbol = "󰏗"
style = "bg:#1d2230 fg:#FCA17D"
format = '[ $symbol $version ]($style)'

# Node.js - 绿色矩阵
[nodejs]
symbol = ""
style = "bg:#1d2230 fg:#3C873A"
format = '[ $symbol ($version) ]($style)'

# Python - 蛇形霓虹
[python]
symbol = ""
style = "bg:#1d2230 fg:#FFDE57"
format = '[ $symbol ($version) ]($style)'

# Rust - 铁锈赛博
[rust]
symbol = ""
style = "bg:#1d2230 fg:#CE422B"
format = '[ $symbol ($version) ]($style)'

# Go - 地鼠电路
[golang]
symbol = ""
style = "bg:#1d2230 fg:#8cc8ff"
format = '[ $symbol ($version) ]($style)'

# Java - 咖啡蒸汽
[java]
symbol = ""
style = "bg:#1d2230 fg:#ED8B00"
format = '[ $symbol ($version) ]($style)'

# Docker - 容器虚拟化
[docker_context]
symbol = ""
style = "bg:#16181d fg:#0db7ed"
format = '[ $symbol $context ]($style)'

# Kubernetes - 集群网络
[kubernetes]
symbol = "󱃾"
style = "bg:#16181d fg:#326ce5"
format = '[ $symbol $context( \($namespace\)) ]($style)'
disabled = false

# AWS - 云端矩阵
[aws]
symbol = ""
style = "bg:#16181d fg:#FFA500"
format = '[ $symbol $profile($region) ]($style)'

# Google Cloud - 量子云
[gcloud]
symbol = "󱇶"
style = "bg:#16181d fg:#4285F4"
format = '[ $symbol $account(@$domain)($region) ]($style)'

# 命令执行时间 - 时空扭曲
[cmd_duration]
min_time = 2_000
style = "fg:#f1fa8c"
format = '[ 󰔛 $duration ]($style)'

# 时间 - 数字时钟
[time]
disabled = false
style = "bg:#0d1117 fg:#ffffff"
format = '[ 󰥔 $time ]($style)'
time_format = "%H:%M:%S"

# 任务数量 - 进程监控
[jobs]
symbol = ""
style = "fg:#a6e3a1"
number_threshold = 1
symbol_threshold = 1
format = '[ $symbol $number ]($style)'

# 内存使用 - 系统监控
[memory_usage]
disabled = false
threshold = 70
symbol = "󰍛"
style = "fg:#f38ba8"
format = '[ $symbol $ram ]($style)'

# 电池 - 能量核心
[battery]
full_symbol = ""
charging_symbol = "󰂄"
discharging_symbol = "󰂃"
unknown_symbol = "󰁽"
empty_symbol = "󰂎"

[[battery.display]]
threshold = 10
style = "fg:#f38ba8"
format = '[ $symbol $percentage ]($style)'

[[battery.display]]
threshold = 30
style = "fg:#f9e2af"
format = '[ $symbol $percentage ]($style)'

[[battery.display]]
threshold = 60
style = "fg:#a6e3a1"
format = '[ $symbol $percentage ]($style)'

[[battery.display]]
threshold = 100
style = "fg:#89b4fa"
format = '[ $symbol $percentage ]($style)'

# C/C++ - 系统级编程
[c]
symbol = ""
style = "bg:#1d2230 fg:#599eff"
format = '[ $symbol ($version) ]($style)'

[cpp]
symbol = ""
style = "bg:#1d2230 fg:#f34b7d"
format = '[ $symbol ($version) ]($style)'

# 其他语言 - 多彩编程宇宙
[bun]
symbol = "🍞"
style = "bg:#1d2230 fg:#fbf0df"
format = '[ $symbol ($version) ]($style)'

[deno]
symbol = "🦕"
style = "bg:#1d2230 fg:#40b883"
format = '[ $symbol ($version) ]($style)'

[dart]
symbol = ""
style = "bg:#1d2230 fg:#0175C2"
format = '[ $symbol ($version) ]($style)'

[elixir]
symbol = ""
style = "bg:#1d2230 fg:#4B275F"
format = '[ $symbol ($version) ]($style)'

[elm]
symbol = ""
style = "bg:#1d2230 fg:#60B5CC"
format = '[ $symbol ($version) ]($style)'

[haskell]
symbol = ""
style = "bg:#1d2230 fg:#5e5086"
format = '[ $symbol ($version) ]($style)'

[julia]
symbol = ""
style = "bg:#1d2230 fg:#9558B2"
format = '[ $symbol ($version) ]($style)'

[kotlin]
symbol = ""
style = "bg:#1d2230 fg:#7F52FF"
format = '[ $symbol ($version) ]($style)'

[lua]
symbol = ""
style = "bg:#1d2230 fg:#2C2D72"
format = '[ $symbol ($version) ]($style)'

[php]
symbol = ""
style = "bg:#1d2230 fg:#777BB4"
format = '[ $symbol ($version) ]($style)'

[ruby]
symbol = ""
style = "bg:#1d2230 fg:#CC342D"
format = '[ $symbol ($version) ]($style)'

[scala]
symbol = ""
style = "bg:#1d2230 fg:#c22d40"
format = '[ $symbol ($version) ]($style)'

[swift]
symbol = ""
style = "bg:#1d2230 fg:#FA7343"
format = '[ $symbol ($version) ]($style)'

[zig]
symbol = ""
style = "bg:#1d2230 fg:#ec915c"
format = '[ $symbol ($version) ]($style)'

# 工具和框架 - 开发工具链
[cmake]
symbol = "△"
style = "bg:#16181d fg:#064F8C"
format = '[ $symbol ($version) ]($style)'

[conda]
symbol = ""
style = "bg:#16181d fg:#44A833"
format = '[ $symbol $environment ]($style)'

[gradle]
symbol = ""
style = "bg:#1d2230 fg:#02303A"
format = '[ $symbol ($version) ]($style)'

[helm]
symbol = "⎈"
style = "bg:#16181d fg:#0F1689"
format = '[ $symbol ($version) ]($style)'

[terraform]
symbol = "󱁢"
style = "bg:#16181d fg:#844FBA"
format = '[ $symbol $workspace ]($style)'

[vagrant]
symbol = "⍱"
style = "bg:#16181d fg:#1563FF"
format = '[ $symbol ($version) ]($style)'

# 其他 - 特殊状态
[sudo]
symbol = "󰞀"
style = "fg:#f38ba8"
format = '[ $symbol ]($style)'

[nix_shell]
symbol = ""
style = "bg:#16181d fg:#7EBAE4"
format = '[ $symbol $state( $name) ]($style)'

# 自定义模块 - 创意元素
[custom.separator]
command = "echo ''"
when = "true"
style = "fg:#6c7086"
format = "[$output]($style)"

[custom.matrix_rain]
command = "echo '󰊠 󰊡 󰊢'"
when = "test $((RANDOM % 10)) -eq 0"
style = "fg:#a6e3a1"
format = "[$output]($style)"