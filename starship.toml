# 🌟 Starship 美化配置
# 现代化、美观的终端提示符配置

# 全局格式设置 - 美化布局
format = """
[╭─](bold blue)$os$username[](bg:#33658A fg:#9A348E)$hostname[](bg:#DA627D fg:#33658A)$directory[](fg:#DA627D)$git_branch$git_status$git_metrics$package$nodejs$python$rust$golang$java$docker_context$kubernetes$aws$gcloud$cmd_duration
[╰─](bold blue)$character"""

# 右侧提示符
right_format = """$time"""

# 字符设置美化
[character]
success_symbol = "[ ➜](bold green)"
error_symbol = "[ ✗](bold red)"
vimcmd_symbol = "[ ](bold yellow)"

# 操作系统图标
[os]
disabled = false
style = "bold blue"

[os.symbols]
Windows = "󰍲 "
Ubuntu = "󰕈 "
SUSE = " "
Raspbian = "󰐿 "
Mint = "󰣭 "
Macos = "󰀵 "
Manjaro = " "
Linux = "󰌽 "
Gentoo = "󰣨 "
Fedora = "󰣛 "
Alpine = " "
Amazon = " "
Android = " "
Arch = "󰣇 "
Artix = "󰣇 "
CentOS = " "
Debian = "󰣚 "
Redhat = "󱄛 "
RedHatEnterprise = "󱄛 "

# 用户名美化
[username]
show_always = true
style_user = "bg:#9A348E fg:#FFFFFF bold"
style_root = "bg:#FF0000 fg:#FFFFFF bold"
format = '[ 󰀄 $user ]($style)'

# 主机名美化
[hostname]
ssh_only = false
style = "bg:#33658A fg:#FFFFFF bold"
format = '[ 󰒋 $hostname ]($style)'

# 目录路径美化
[directory]
style = "bg:#DA627D fg:#FFFFFF bold"
format = "[ 󰉋 $path ]($style)[$read_only]($read_only_style)"
truncation_length = 4
truncation_symbol = "…/"
read_only = " 󰌾"
read_only_style = "bg:#DA627D fg:#FFFF00"
home_symbol = "󰋜 ~"
use_logical_path = true

# 目录替换规则 - 更多美化图标
[directory.substitutions]
"Documents" = "󰈙 文档"
"Downloads" = " 下载"
"Music" = "󰝚 音乐"
"Pictures" = " 图片"
"Videos" = " 视频"
"Desktop" = "󰧨 桌面"
"Projects" = "󰲋 项目"
"Code" = " 代码"
"src" = " 源码"
"config" = " 配置"
"bin" = " 二进制"
"lib" = "󰲋 库文件"
"node_modules" = " 模块"
".config" = " 配置"
".git" = " Git"
"target" = "󰯁 目标"
"build" = "󰯁 构建"
"dist" = "󰯁 发布"

# Git 分支
[git_branch]
symbol = " "
style = "bold green"
format = "on [$symbol$branch]($style) "

# Git 状态
[git_status]
style = "bold yellow"
format = '([\[$all_status$ahead_behind\]]($style) )'
conflicted = "⚡"
ahead = "⇡${count}"
behind = "⇣${count}"
diverged = "⇕⇡${ahead_count}⇣${behind_count}"
up_to_date = "✓"
untracked = "?${count}"
stashed = "📦"
modified = "!${count}"
staged = "+${count}"
renamed = "»${count}"
deleted = "✘${count}"

# Git 指标
[git_metrics]
disabled = false
added_style = "bold green"
deleted_style = "bold red"
format = '([+$added]($added_style) )([-$deleted]($deleted_style) )'

# 包管理器
[package]
symbol = "󰏗 "
style = "bold cyan"
format = "[$symbol$version]($style) "

# Node.js
[nodejs]
symbol = " "
style = "bold green"
format = "[$symbol($version)]($style) "

# Python
[python]
symbol = " "
style = "bold yellow"
format = '[${symbol}${pyenv_prefix}(${version})(\($virtualenv\))]($style) '

# Rust
[rust]
symbol = " "
style = "bold orange"
format = "[$symbol($version)]($style) "

# Go
[golang]
symbol = " "
style = "bold cyan"
format = "[$symbol($version)]($style) "

# Java
[java]
symbol = " "
style = "bold red"
format = "[$symbol($version)]($style) "

# Docker
[docker_context]
symbol = " "
style = "bold blue"
format = "[$symbol$context]($style) "

# Kubernetes
[kubernetes]
symbol = "󱃾 "
style = "bold blue"
format = '[$symbol$context( \($namespace\))]($style) '
disabled = false

# AWS
[aws]
symbol = "  "
style = "bold orange"
format = '[$symbol($profile)(\($region\))(\[$duration\])]($style) '

# Google Cloud
[gcloud]
symbol = "󱇶 "
style = "bold blue"
format = '[$symbol$account(@$domain)(\($region\))]($style) '

# 命令执行时间
[cmd_duration]
min_time = 2_000
style = "bold yellow"
format = "took [$duration]($style) "

# 时间
[time]
disabled = false
style = "bold white"
format = "[$time]($style)"
time_format = "%H:%M:%S"

# 内存使用
[memory_usage]
disabled = false
threshold = 70
symbol = "󰍛 "
style = "bold dimmed red"
format = "[$symbol[$ram( | $swap)]($style) "

# 电池
[battery]
full_symbol = "🔋"
charging_symbol = "🔌"
discharging_symbol = "⚡"

[[battery.display]]
threshold = 10
style = "bold red"

[[battery.display]]
threshold = 30
style = "bold yellow"

# C/C++
[c]
symbol = " "
style = "bold blue"
format = "[$symbol($version(-$name))]($style) "

[cpp]
symbol = " "
style = "bold blue"
format = "[$symbol($version(-$name))]($style) "

# 其他语言
[bun]
symbol = "🍞 "
style = "bold yellow"
format = "[$symbol($version)]($style) "

[deno]
symbol = "🦕 "
style = "bold green"
format = "[$symbol($version)]($style) "

[dart]
symbol = " "
style = "bold blue"
format = "[$symbol($version)]($style) "

[elixir]
symbol = " "
style = "bold purple"
format = "[$symbol($version \\(OTP $otp_version\\))]($style) "

[elm]
symbol = " "
style = "bold cyan"
format = "[$symbol($version)]($style) "

[haskell]
symbol = " "
style = "bold purple"
format = "[$symbol($version)]($style) "

[julia]
symbol = " "
style = "bold purple"
format = "[$symbol($version)]($style) "

[kotlin]
symbol = " "
style = "bold blue"
format = "[$symbol($version)]($style) "

[lua]
symbol = " "
style = "bold blue"
format = "[$symbol($version)]($style) "

[php]
symbol = " "
style = "bold purple"
format = "[$symbol($version)]($style) "

[ruby]
symbol = " "
style = "bold red"
format = "[$symbol($version)]($style) "

[scala]
symbol = " "
style = "bold red"
format = "[$symbol($version)]($style) "

[swift]
symbol = " "
style = "bold orange"
format = "[$symbol($version)]($style) "

[zig]
symbol = " "
style = "bold yellow"
format = "[$symbol($version)]($style) "

# 工具和框架
[cmake]
symbol = "△ "
style = "bold blue"
format = "[$symbol($version)]($style) "

[conda]
symbol = "🅒 "
style = "bold green"
format = "[$symbol$environment]($style) "

[gradle]
symbol = " "
style = "bold cyan"
format = "[$symbol($version)]($style) "

[helm]
symbol = "⎈ "
style = "bold blue"
format = "[$symbol($version)]($style) "

[terraform]
symbol = "󱁢 "
style = "bold purple"
format = "[$symbol$workspace]($style) "

[vagrant]
symbol = "⍱ "
style = "bold blue"
format = "[$symbol($version)]($style) "

# 其他
[sudo]
symbol = "🧙 "
style = "bold red"
format = "[as $symbol]($style)"

[nix_shell]
symbol = " "
style = "bold blue"
format = "[$symbol$state( \\($name\\))]($style) "