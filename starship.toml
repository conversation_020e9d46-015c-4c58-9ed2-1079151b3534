# ✨ 简洁美观的 Starship 配置
# 现代化、简约风格的终端提示符

# 全局格式设置 - 简洁单行
format = """$username$hostname$directory$git_branch$git_status$package$nodejs$python$rust$golang$java$docker_context$cmd_duration$character"""

# 右侧提示符
right_format = """$time"""

# 字符提示符 - 简洁风格
[character]
success_symbol = "[▶](bold green)"
error_symbol = "[✕](bold red)"
vimcmd_symbol = "[◀](bold yellow)"

# 用户名 - 简洁风格
[username]
show_always = true
style_user = "bold cyan"
style_root = "bold red"
format = '[$user](bold cyan) '

# 主机名 - 简洁风格
[hostname]
ssh_only = false
style = "bold blue"
format = '[@$hostname](bold blue) '

# 目录路径 - 简洁美观
[directory]
style = "bold purple"
format = "[$path](bold purple) "
truncation_length = 3
truncation_symbol = "…/"
read_only = "🔒"
home_symbol = "~"
use_logical_path = true

# 目录替换 - 简洁图标
[directory.substitutions]
"Documents" = "📄 文档"
"Downloads" = "📥 下载"
"Music" = "🎵 音乐"
"Pictures" = "🖼️ 图片"
"Videos" = "🎬 视频"
"Desktop" = "🖥️ 桌面"
"Projects" = "📁 项目"
"Code" = "💻 代码"
"src" = "📂 源码"
"config" = "⚙️ 配置"
".config" = "⚙️ 配置"
"node_modules" = "📦 模块"
".git" = "🔧 Git"

# Git 分支 - 简洁风格
[git_branch]
symbol = "●"
style = "bold green"
format = "[$symbol $branch](bold green) "

# Git 状态 - 简洁符号
[git_status]
style = "bold yellow"
format = '([$all_status$ahead_behind](bold yellow) )'
conflicted = "!"
ahead = "↑${count}"
behind = "↓${count}"
diverged = "↕${ahead_count}${behind_count}"
up_to_date = "✓"
untracked = "?${count}"
stashed = "≡"
modified = "~${count}"
staged = "+${count}"
renamed = "»${count}"
deleted = "-${count}"

# 包管理器 - 简洁
[package]
symbol = "📦"
style = "bold cyan"
format = "[$symbol $version](bold cyan) "

# Node.js - 简洁
[nodejs]
symbol = "◆"
style = "bold green"
format = "[$symbol $version](bold green) "

# Python - 简洁
[python]
symbol = "▲"
style = "bold yellow"
format = '[$symbol $version](bold yellow) '

# Rust - 简洁
[rust]
symbol = "●"
style = "bold orange"
format = "[$symbol $version](bold orange) "

# Go - 简洁
[golang]
symbol = "◇"
style = "bold cyan"
format = "[$symbol $version](bold cyan) "

# Java - 简洁
[java]
symbol = "◈"
style = "bold red"
format = "[$symbol $version](bold red) "

# Docker - 简洁
[docker_context]
symbol = "◐"
style = "bold blue"
format = "[$symbol $context](bold blue) "

# Kubernetes - 简洁
[kubernetes]
symbol = "⎈"
style = "bold blue"
format = '[$symbol $context](bold blue) '
disabled = false

# 命令执行时间 - 简洁
[cmd_duration]
min_time = 2_000
style = "bold yellow"
format = "[⏱ $duration](bold yellow) "

# 时间 - 简洁
[time]
disabled = false
style = "bold white"
format = "[$time](bold white)"
time_format = "%H:%M"

