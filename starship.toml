# 🌈 极致创意 Starship 配置
# 突破传统 + 艺术美学 + 动态交互

# 全局格式设置 - 流动艺术风格
format = """
[╭─](bold blue)$os$username$hostname$directory$git_branch$git_status$git_metrics$package$nodejs$python$rust$golang$java$docker_context$kubernetes$aws$gcloud$cmd_duration$custom.weather$custom.quote
[╰─](bold blue)$character"""

# 右侧提示符 - 动态信息流
right_format = """$time$battery$memory_usage$custom.system_load"""

# 字符设置 - 动态表情符号
[character]
success_symbol = "[✨](bold green)"
error_symbol = "[💥](bold red)"
vimcmd_symbol = "[🎯](bold yellow)"

# 换行设置
[line_break]
disabled = false

# 操作系统 - 彩虹图标
[os]
disabled = false
style = "bold blue"

[os.symbols]
Windows = "🪟 "
Ubuntu = "🐧 "
SUSE = "🦎 "
Raspbian = "🍓 "
Mint = "🌿 "
Macos = "🍎 "
Manjaro = "🥭 "
Linux = "🐧 "
Gentoo = "🐧 "
Fedora = "🎩 "
Alpine = "🏔️ "
Amazon = "☁️ "
Android = "🤖 "
Arch = "🏛️ "
Artix = "🎨 "
CentOS = "🌀 "
Debian = "🌊 "
Redhat = "🎩 "
RedHatEnterprise = "🏢 "

# 用户名 - 个性化头像
[username]
show_always = true
style_user = "bold yellow"
style_root = "bold red"
format = "🧑‍💻 [$user]($style) "

# 主机名 - 网络节点
[hostname]
ssh_only = false
style = "bold cyan"
format = "🌐 [@$hostname]($style) "

# 目录 - 表情符号路径
[directory]
style = "bold purple"
format = "📁 [$path]($style)[$read_only]($read_only_style) "
truncation_length = 3
truncation_symbol = "…/"
read_only = "🔒"

[directory.substitutions]
"Documents" = "📄 "
"Downloads" = "⬇️ "
"Music" = "🎵 "
"Pictures" = "🖼️ "
"Videos" = "🎬 "
"Desktop" = "🖥️ "
"Projects" = "🚀 "
"Code" = "💻 "
".config" = "⚙️ "
"src" = "📦 "
"node_modules" = "📚 "
".git" = "🌳 "
"Windows" = "🪟 "
"System32" = "⚙️ "
"Program Files" = "📁 "
"Users" = "👥 "

# Git 分支 - 生动表情
[git_branch]
symbol = "🌿 "
style = "bold green"
format = "on [$symbol$branch]($style) "

# Git 状态 - 表情符号状态
[git_status]
style = "bold yellow"
format = '([\[$all_status$ahead_behind\]]($style) )'
conflicted = "⚔️"
ahead = "🚀${count}"
behind = "🐌${count}"
diverged = "🔀🚀${ahead_count}🐌${behind_count}"
up_to_date = "✅"
untracked = "❓${count}"
stashed = "📦${count}"
modified = "✏️${count}"
staged = "➕${count}"
renamed = "📝${count}"
deleted = "🗑️${count}"

# Git 指标 - 统计表情
[git_metrics]
disabled = false
added_style = "bold green"
deleted_style = "bold red"
format = '([➕$added]($added_style) )([➖$deleted]($deleted_style) )'

# 包管理器 - 礼品盒
[package]
symbol = "📦 "
style = "bold cyan"
format = "[$symbol$version]($style) "

# Node.js - 绿色能量
[nodejs]
symbol = "⚡ "
style = "bold green"
format = "[$symbol($version)]($style) "

# Python - 蛇蛇
[python]
symbol = "🐍 "
style = "bold yellow"
format = '[${symbol}${pyenv_prefix}(${version})(\($virtualenv\))]($style) '

# Rust - 齿轮
[rust]
symbol = "⚙️ "
style = "bold orange"
format = "[$symbol($version)]($style) "

# Go - 火箭
[golang]
symbol = "🚀 "
style = "bold cyan"
format = "[$symbol($version)]($style) "

# Java - 咖啡
[java]
symbol = "☕ "
style = "bold red"
format = "[$symbol($version)]($style) "

# Docker - 鲸鱼容器
[docker_context]
symbol = "🐳 "
style = "bold blue"
format = "[$symbol$context]($style) "

# Kubernetes - 舵轮
[kubernetes]
symbol = "⎈ "
style = "bold blue"
format = '[$symbol$context( \($namespace\))]($style) '
disabled = false

# AWS - 云朵
[aws]
symbol = "☁️ "
style = "bold orange"
format = '[$symbol($profile)(\($region\))(\[$duration\])]($style) '

# Google Cloud - 彩云
[gcloud]
symbol = "🌈 "
style = "bold blue"
format = '[$symbol$account(@$domain)(\($region\))]($style) '

# 命令执行时间 - 计时器
[cmd_duration]
min_time = 2_000
style = "bold yellow"
format = "⏱️ took [$duration]($style) "

# 时间 - 时钟
[time]
disabled = false
style = "bold white"
format = "🕐 [$time]($style)"
time_format = "%H:%M:%S"

# 任务数量 - 工作台
[jobs]
symbol = "🔧"
style = "bold cyan"
number_threshold = 1
symbol_threshold = 1
format = '[$symbol $number]($style) '

# 内存使用 - 系统监控
[memory_usage]
disabled = false
threshold = 70
symbol = "󰍛"
style = "fg:#f38ba8"
format = '[ $symbol $ram ]($style)'

# 电池 - 能量核心
[battery]
full_symbol = ""
charging_symbol = "󰂄"
discharging_symbol = "󰂃"
unknown_symbol = "󰁽"
empty_symbol = "󰂎"

[[battery.display]]
threshold = 10
style = "fg:#f38ba8"
format = '[ $symbol $percentage ]($style)'

[[battery.display]]
threshold = 30
style = "fg:#f9e2af"
format = '[ $symbol $percentage ]($style)'

[[battery.display]]
threshold = 60
style = "fg:#a6e3a1"
format = '[ $symbol $percentage ]($style)'

[[battery.display]]
threshold = 100
style = "fg:#89b4fa"
format = '[ $symbol $percentage ]($style)'

# C/C++ - 系统级编程
[c]
symbol = ""
style = "bg:#1d2230 fg:#599eff"
format = '[ $symbol ($version) ]($style)'

[cpp]
symbol = ""
style = "bg:#1d2230 fg:#f34b7d"
format = '[ $symbol ($version) ]($style)'

# 其他语言 - 多彩编程宇宙
[bun]
symbol = "🍞"
style = "bg:#1d2230 fg:#fbf0df"
format = '[ $symbol ($version) ]($style)'

[deno]
symbol = "🦕"
style = "bg:#1d2230 fg:#40b883"
format = '[ $symbol ($version) ]($style)'

[dart]
symbol = ""
style = "bg:#1d2230 fg:#0175C2"
format = '[ $symbol ($version) ]($style)'

[elixir]
symbol = ""
style = "bg:#1d2230 fg:#4B275F"
format = '[ $symbol ($version) ]($style)'

[elm]
symbol = ""
style = "bg:#1d2230 fg:#60B5CC"
format = '[ $symbol ($version) ]($style)'

[haskell]
symbol = ""
style = "bg:#1d2230 fg:#5e5086"
format = '[ $symbol ($version) ]($style)'

[julia]
symbol = ""
style = "bg:#1d2230 fg:#9558B2"
format = '[ $symbol ($version) ]($style)'

[kotlin]
symbol = ""
style = "bg:#1d2230 fg:#7F52FF"
format = '[ $symbol ($version) ]($style)'

[lua]
symbol = ""
style = "bg:#1d2230 fg:#2C2D72"
format = '[ $symbol ($version) ]($style)'

[php]
symbol = ""
style = "bg:#1d2230 fg:#777BB4"
format = '[ $symbol ($version) ]($style)'

[ruby]
symbol = ""
style = "bg:#1d2230 fg:#CC342D"
format = '[ $symbol ($version) ]($style)'

[scala]
symbol = ""
style = "bg:#1d2230 fg:#c22d40"
format = '[ $symbol ($version) ]($style)'

[swift]
symbol = ""
style = "bg:#1d2230 fg:#FA7343"
format = '[ $symbol ($version) ]($style)'

[zig]
symbol = ""
style = "bg:#1d2230 fg:#ec915c"
format = '[ $symbol ($version) ]($style)'

# 工具和框架 - 开发工具链
[cmake]
symbol = "△"
style = "bg:#16181d fg:#064F8C"
format = '[ $symbol ($version) ]($style)'

[conda]
symbol = ""
style = "bg:#16181d fg:#44A833"
format = '[ $symbol $environment ]($style)'

[gradle]
symbol = ""
style = "bg:#1d2230 fg:#02303A"
format = '[ $symbol ($version) ]($style)'

[helm]
symbol = "⎈"
style = "bg:#16181d fg:#0F1689"
format = '[ $symbol ($version) ]($style)'

[terraform]
symbol = "󱁢"
style = "bg:#16181d fg:#844FBA"
format = '[ $symbol $workspace ]($style)'

[vagrant]
symbol = "⍱"
style = "bg:#16181d fg:#1563FF"
format = '[ $symbol ($version) ]($style)'

# 其他 - 特殊状态
[sudo]
symbol = "󰞀"
style = "fg:#f38ba8"
format = '[ $symbol ]($style)'

[nix_shell]
symbol = ""
style = "bg:#16181d fg:#7EBAE4"
format = '[ $symbol $state( $name) ]($style)'

# 🎨 超级创意自定义模块

# 随机天气心情
[custom.weather]
command = '''
weather_emojis=("☀️" "🌤️" "⛅" "🌦️" "🌧️" "⛈️" "🌩️" "❄️" "🌨️" "🌪️" "🌈" "⭐" "🌙" "💫")
echo ${weather_emojis[$((RANDOM % ${#weather_emojis[@]}))]}
'''
when = "test $((RANDOM % 5)) -eq 0"
style = "bold blue"
format = "[$output]($style)"
shell = ["bash", "-c"]

# 随机励志语录
[custom.quote]
command = '''
quotes=("💪" "🔥" "⚡" "🚀" "✨" "🎯" "💎" "🏆" "🌟" "🎉" "🎊" "🎈" "🎁" "🎀")
echo ${quotes[$((RANDOM % ${#quotes[@]}))]}
'''
when = "test $((RANDOM % 8)) -eq 0"
style = "bold magenta"
format = "[$output]($style)"
shell = ["bash", "-c"]

# 系统负载指示器
[custom.system_load]
command = '''
if command -v wmic >/dev/null 2>&1; then
    cpu_usage=$(wmic cpu get loadpercentage /value | grep -o "[0-9]*" | head -1)
    if [ "$cpu_usage" -gt 80 ]; then
        echo "🔥"
    elif [ "$cpu_usage" -gt 50 ]; then
        echo "⚡"
    else
        echo "😎"
    fi
else
    echo "💻"
fi
'''
when = "true"
style = "bold cyan"
format = "[$output]($style)"
shell = ["bash", "-c"]

# 时间段问候
[custom.greeting]
command = '''
hour=$(date +%H)
if [ $hour -ge 6 ] && [ $hour -lt 12 ]; then
    echo "🌅"
elif [ $hour -ge 12 ] && [ $hour -lt 18 ]; then
    echo "☀️"
elif [ $hour -ge 18 ] && [ $hour -lt 22 ]; then
    echo "🌆"
else
    echo "🌙"
fi
'''
when = "test $((RANDOM % 10)) -eq 0"
style = "bold yellow"
format = "[$output]($style)"
shell = ["bash", "-c"]

# 随机编程表情
[custom.coding_mood]
command = '''
moods=("🤖" "👨‍💻" "👩‍💻" "🧠" "💡" "🔧" "⚙️" "🛠️" "🔬" "🧪" "📊" "📈" "🎮" "🕹️")
echo ${moods[$((RANDOM % ${#moods[@]}))]}
'''
when = "test $((RANDOM % 15)) -eq 0"
style = "bold green"
format = "[$output]($style)"
shell = ["bash", "-c"]